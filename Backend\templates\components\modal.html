<!-- Reusable HTMX Modal Component -->
{% load django_htmx %}

<div 
    id="{{ modal_id|default:'modal' }}" 
    class="fixed inset-0 z-50 overflow-y-auto"
    x-data="{ open: true }"
    x-show="open"
    x-transition:enter="ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
>
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
    
    <!-- Modal panel -->
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div 
            class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
            x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            @click.away="open = false"
        >
            <!-- Modal header -->
            {% if title %}
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left flex-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                            {{ title }}
                        </h3>
                        {% if subtitle %}
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                {{ subtitle }}
                            </p>
                        </div>
                        {% endif %}
                    </div>
                    <!-- Close button -->
                    <button 
                        type="button" 
                        class="ml-3 inline-flex h-8 w-8 items-center justify-center rounded-full bg-transparent text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        @click="open = false"
                        hx-get="{{ close_url|default:'#' }}"
                        hx-target="#modal-container"
                        hx-swap="innerHTML"
                    >
                        <span class="sr-only">Close</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            {% endif %}
            
            <!-- Modal content -->
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                {% if form_url %}
                <form 
                    {% if form_method == 'post' %}hx-post="{{ form_url }}"{% else %}hx-get="{{ form_url }}"{% endif %}
                    {% if form_target %}hx-target="{{ form_target }}"{% endif %}
                    {% if form_swap %}hx-swap="{{ form_swap }}"{% endif %}
                    hx-indicator="#modal-loading"
                >
                    {% csrf_token %}
                    {{ content|safe }}
                </form>
                {% else %}
                    {{ content|safe }}
                {% endif %}
            </div>
            
            <!-- Modal footer -->
            {% if show_footer %}
            <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                {% if actions %}
                    {{ actions|safe }}
                {% else %}
                    <!-- Default actions -->
                    {% if form_url %}
                    <button 
                        type="submit" 
                        class="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                        <span class="htmx-indicator">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </span>
                        <span class="htmx-indicator">
                            {{ submit_text|default:'Save' }}
                        </span>
                    </button>
                    {% endif %}
                    
                    <button 
                        type="button" 
                        class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        @click="open = false"
                    >
                        {{ cancel_text|default:'Cancel' }}
                    </button>
                {% endif %}
            </div>
            {% endif %}
            
            <!-- Loading indicator -->
            <div id="modal-loading" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <div class="flex items-center space-x-2">
                    <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-focus first input in modal
    document.addEventListener('DOMContentLoaded', function() {
        const firstInput = document.querySelector('#{{ modal_id|default:"modal" }} input, #{{ modal_id|default:"modal" }} textarea, #{{ modal_id|default:"modal" }} select');
        if (firstInput) {
            firstInput.focus();
        }
    });
    
    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.querySelector('#{{ modal_id|default:"modal" }}');
            if (modal) {
                Alpine.store('modal').close();
            }
        }
    });
</script>

<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
  <!-- Card item -->
  <div
    class="rounded-2xl border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-white/[0.03] md:p-6"
  >
    <div class="flex items-start justify-between">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">
        Top Channels
      </h3>

      <div x-data="{openDropDown: false}" class="relative">
        <button
          @click="openDropDown = !openDropDown"
          :class="openDropDown ? 'text-gray-700 dark:text-white' : 'text-gray-400 hover:text-gray-700 dark:hover:text-white'"
        >
          <svg
            class="fill-current"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M10.2441 6C10.2441 5.0335 11.0276 4.25 11.9941 4.25H12.0041C12.9706 4.25 13.7541 5.0335 13.7541 6C13.7541 6.9665 12.9706 7.75 12.0041 7.75H11.9941C11.0276 7.75 10.2441 6.9665 10.2441 6ZM10.2441 18C10.2441 17.0335 11.0276 16.25 11.9941 16.25H12.0041C12.9706 16.25 13.7541 17.0335 13.7541 18C13.7541 18.9665 12.9706 19.75 12.0041 19.75H11.9941C11.0276 19.75 10.2441 18.9665 10.2441 18ZM11.9941 10.25C11.0276 10.25 10.2441 11.0335 10.2441 12C10.2441 12.9665 11.0276 13.75 11.9941 13.75H12.0041C12.9706 13.75 13.7541 12.9665 13.7541 12C13.7541 11.0335 12.9706 10.25 12.0041 10.25H11.9941Z"
              fill=""
            />
          </svg>
        </button>
        <div
          x-show="openDropDown"
          @click.outside="openDropDown = false"
          class="absolute right-0 top-full z-40 w-40 space-y-1 rounded-2xl border border-gray-200 bg-white p-2 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark"
        >
          <button
            class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
          >
            View More
          </button>
          <button
            class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <div class="my-6">
      <div
        class="flex items-center justify-between border-b border-gray-100 pb-4 dark:border-gray-800"
      >
        <span class="text-theme-xs text-gray-400"> Source </span>
        <span class="text-right text-theme-xs text-gray-400"> Visitors </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          Google
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          4.7K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          Facebook
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          3.4K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          Threads
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          2.9K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          Google
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          1.5K
        </span>
      </div>
    </div>

    <a
      href="#"
      class="flex justify-center gap-2 rounded-lg border border-gray-300 bg-white p-2.5 text-theme-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03]"
    >
      Channels Report
      <svg
        class="fill-current"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.4175 9.9986C17.4178 10.1909 17.3446 10.3832 17.198 10.53L12.2013 15.5301C11.9085 15.8231 11.4337 15.8233 11.1407 15.5305C10.8477 15.2377 10.8475 14.7629 11.1403 14.4699L14.8604 10.7472L3.33301 10.7472C2.91879 10.7472 2.58301 10.4114 2.58301 9.99715C2.58301 9.58294 2.91879 9.24715 3.33301 9.24715L14.8549 9.24715L11.1403 5.53016C10.8475 5.23717 10.8477 4.7623 11.1407 4.4695C11.4336 4.1767 11.9085 4.17685 12.2013 4.46984L17.1588 9.43049C17.3173 9.568 17.4175 9.77087 17.4175 9.99715C17.4175 9.99763 17.4175 9.99812 17.4175 9.9986Z"
          fill=""
        />
      </svg>
    </a>
  </div>

  <!-- Card item -->
  <div
    class="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6"
  >
    <div class="flex items-start justify-between">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white/90">
        Top Pages
      </h3>

      <div x-data="{openDropDown: false}" class="relative">
        <button
          @click="openDropDown = !openDropDown"
          :class="openDropDown ? 'text-gray-700 dark:text-white' : 'text-gray-400 hover:text-gray-700 dark:hover:text-white'"
        >
          <svg
            class="fill-current"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M10.2441 6C10.2441 5.0335 11.0276 4.25 11.9941 4.25H12.0041C12.9706 4.25 13.7541 5.0335 13.7541 6C13.7541 6.9665 12.9706 7.75 12.0041 7.75H11.9941C11.0276 7.75 10.2441 6.9665 10.2441 6ZM10.2441 18C10.2441 17.0335 11.0276 16.25 11.9941 16.25H12.0041C12.9706 16.25 13.7541 17.0335 13.7541 18C13.7541 18.9665 12.9706 19.75 12.0041 19.75H11.9941C11.0276 19.75 10.2441 18.9665 10.2441 18ZM11.9941 10.25C11.0276 10.25 10.2441 11.0335 10.2441 12C10.2441 12.9665 11.0276 13.75 11.9941 13.75H12.0041C12.9706 13.75 13.7541 12.9665 13.7541 12C13.7541 11.0335 12.9706 10.25 12.0041 10.25H11.9941Z"
              fill=""
            />
          </svg>
        </button>
        <div
          x-show="openDropDown"
          @click.outside="openDropDown = false"
          class="absolute right-0 top-full z-40 w-40 space-y-1 rounded-2xl border border-gray-200 bg-white p-2 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark"
        >
          <button
            class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
          >
            View More
          </button>
          <button
            class="flex w-full rounded-lg px-3 py-2 text-left text-theme-xs font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <div class="my-6">
      <div
        class="flex items-center justify-between border-b border-gray-100 pb-4 dark:border-gray-800"
      >
        <span class="text-theme-xs text-gray-400"> Source </span>
        <span class="text-right text-theme-xs text-gray-400"> Pageview </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          tailadmin.com
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          4.7K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          preview.tailadmin.com
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          3.4K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          docs.tailadmin.com
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          2.9K
        </span>
      </div>

      <div
        class="flex items-center justify-between border-b border-gray-100 py-3 dark:border-gray-800"
      >
        <span class="text-theme-sm text-gray-500 dark:text-gray-400">
          tailadmin.com/componetns
        </span>
        <span class="text-right text-theme-sm text-gray-500 dark:text-gray-400">
          1.5K
        </span>
      </div>
    </div>

    <a
      href="#"
      class="flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white p-2.5 text-theme-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03]"
    >
      Channels Report
      <svg
        class="fill-current"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.4175 9.9986C17.4178 10.1909 17.3446 10.3832 17.198 10.53L12.2013 15.5301C11.9085 15.8231 11.4337 15.8233 11.1407 15.5305C10.8477 15.2377 10.8475 14.7629 11.1403 14.4699L14.8604 10.7472L3.33301 10.7472C2.91879 10.7472 2.58301 10.4114 2.58301 9.99715C2.58301 9.58294 2.91879 9.24715 3.33301 9.24715L14.8549 9.24715L11.1403 5.53016C10.8475 5.23717 10.8477 4.7623 11.1407 4.4695C11.4336 4.1767 11.9085 4.17685 12.2013 4.46984L17.1588 9.43049C17.3173 9.568 17.4175 9.77087 17.4175 9.99715C17.4175 9.99763 17.4175 9.99812 17.4175 9.9986Z"
          fill=""
        />
      </svg>
    </a>
  </div>
</div>

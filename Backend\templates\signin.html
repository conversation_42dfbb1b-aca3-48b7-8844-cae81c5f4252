{% load static tailwind_tags %}
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Admin Sign In | ShuleXcel</title>
    {% tailwind_css %}
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  </head>
  <body
    x-data="{ 'loaded': true, 'darkMode': false }"
    x-init="
         darkMode = JSON.parse(localStorage.getItem('darkMode'));
         $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)))"
    :class="{'dark bg-gray-900': darkMode === true}"
    class="bg-gray-50"
  >
    <!-- ===== Page Wrapper Start ===== -->
    <div class="relative p-6 bg-white z-1 dark:bg-gray-900">
      <div class="relative flex flex-col justify-center w-full h-screen dark:bg-gray-900 lg:flex-row">
        
        <!-- Form Section -->
        <div class="flex flex-col flex-1 w-full lg:w-1/2">
          <div class="w-full max-w-md pt-10 mx-auto">
            <a
              href="{% url 'index' %}"
              class="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 mb-8"
            >
              <svg
                class="stroke-current mr-2"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M12.7083 5L7.5 10.2083L12.7083 15.4167"
                  stroke=""
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Back to Home
            </a>

            <!-- Logo -->
            <div class="mb-8">
              <img
                class="h-12 w-auto"
                src="{% static 'images/logo/logo.svg' %}"
                alt="ShuleXcel"
              />
            </div>

            <!-- Sign In Form -->
            <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-8">
              <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                  Sign in to Admin Panel
                </h1>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Enter your credentials to access the admin dashboard
                </p>
              </div>

              <form method="post" action="{% url 'shule_admin:signin' %}" class="space-y-6">
                {% csrf_token %}
                
                <!-- Email Field -->
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </label>
                  <div class="mt-1">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autocomplete="email"
                      required
                      class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="Enter your email"
                    />
                  </div>
                </div>

                <!-- Password Field -->
                <div>
                  <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Password
                  </label>
                  <div class="mt-1">
                    <input
                      id="password"
                      name="password"
                      type="password"
                      autocomplete="current-password"
                      required
                      class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="Enter your password"
                    />
                  </div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                      Remember me
                    </label>
                  </div>

                  <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                      Forgot your password?
                    </a>
                  </div>
                </div>

                <!-- Sign In Button -->
                <div>
                  <button
                    type="submit"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Sign in
                  </button>
                </div>

                <!-- Error Messages -->
                {% if messages %}
                  <div class="mt-4">
                    {% for message in messages %}
                      <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ message }}</span>
                      </div>
                    {% endfor %}
                  </div>
                {% endif %}
              </form>
            </div>
          </div>
        </div>

        <!-- Image Section -->
        <div class="hidden lg:flex lg:w-1/2 lg:items-center lg:justify-center">
          <div class="max-w-md">
            <img
              class="w-full h-auto"
              src="{% static 'images/admin-signin.svg' %}"
              alt="Admin Sign In"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- ===== Page Wrapper End ===== -->
  </body>
</html>

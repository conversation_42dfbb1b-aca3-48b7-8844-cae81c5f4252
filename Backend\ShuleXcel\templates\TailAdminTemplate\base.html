{% load static tailwind_tags %}
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>{% block title %}Admin Dashboard | ShuleXcel{% endblock %}</title>
    {% tailwind_css %}
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
      .sidebar {
        transition: all 0.3s ease;
      }
      .no-scrollbar::-webkit-scrollbar {
        display: none;
      }
      .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    </style>
    
    {% block extra_css %}{% endblock %}
  </head>
  <body
    x-data="{ 
      page: '{% block page %}dashboard{% endblock %}', 
      'loaded': true, 
      'darkMode': false, 
      'stickyMenu': false, 
      'sidebarToggle': false, 
      'scrollTop': false 
    }"
    x-init="
         darkMode = JSON.parse(localStorage.getItem('darkMode'));
         $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)))"
    :class="{'dark bg-gray-900': darkMode === true}"
  >
    <!-- ===== Preloader Start ===== -->
    {% include 'admin/partials/preloader.html' %}
    <!-- ===== Preloader End ===== -->

    <!-- ===== Page Wrapper Start ===== -->
    <div class="flex h-screen overflow-hidden">
      <!-- ===== Sidebar Start ===== -->
      {% include 'admin/partials/sidebar.html' %}
      <!-- ===== Sidebar End ===== -->

      <!-- ===== Content Area Start ===== -->
      <div
        class="relative flex flex-col flex-1 overflow-x-hidden overflow-y-auto"
      >
        <!-- Small Device Overlay Start -->
        {% include 'admin/partials/overlay.html' %}
        <!-- Small Device Overlay End -->

        <!-- ===== Header Start ===== -->
        {% include 'admin/partials/header.html' %}
        <!-- ===== Header End ===== -->

        <!-- ===== Main Content Start ===== -->
        <main>
          <div class="p-4 mx-auto max-w-(--breakpoint-2xl) md:p-6">
            {% block content %}
            <!-- Main content goes here -->
            {% endblock %}
          </div>
        </main>
        <!-- ===== Main Content End ===== -->
      </div>
      <!-- ===== Content Area End ===== -->
    </div>
    <!-- ===== Page Wrapper End ===== -->

    <!-- Scripts -->
    <script>
      // Dark mode toggle
      function toggleDarkMode() {
        const darkMode = localStorage.getItem('darkMode') === 'true';
        localStorage.setItem('darkMode', !darkMode);
        location.reload();
      }
    </script>
    
    {% block extra_js %}{% endblock %}
  </body>
</html>

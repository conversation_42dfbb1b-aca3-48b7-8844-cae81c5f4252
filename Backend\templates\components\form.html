<!-- Reusable HTMX Form Component -->
{% load django_htmx %}

<form 
    {% if action %}action="{{ action }}"{% endif %}
    {% if method %}method="{{ method }}"{% else %}method="post"{% endif %}
    {% if hx_post %}hx-post="{{ hx_post }}"{% endif %}
    {% if hx_get %}hx-get="{{ hx_get }}"{% endif %}
    {% if hx_put %}hx-put="{{ hx_put }}"{% endif %}
    {% if hx_delete %}hx-delete="{{ hx_delete }}"{% endif %}
    {% if hx_target %}hx-target="{{ hx_target }}"{% endif %}
    {% if hx_swap %}hx-swap="{{ hx_swap }}"{% endif %}
    {% if hx_trigger %}hx-trigger="{{ hx_trigger }}"{% endif %}
    {% if hx_indicator %}hx-indicator="{{ hx_indicator }}"{% endif %}
    {% if hx_vals %}hx-vals="{{ hx_vals }}"{% endif %}
    {% if enctype %}enctype="{{ enctype }}"{% endif %}
    class="space-y-6 {{ form_class }}"
    {% if form_id %}id="{{ form_id }}"{% endif %}
>
    {% csrf_token %}
    
    <!-- Form errors -->
    {% if form.non_field_errors %}
    <div class="rounded-md bg-red-50 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                    There were errors with your submission
                </h3>
                <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Form fields -->
    {% if fields %}
        {% for field_config in fields %}
            {% include 'components/form_field.html' with field=field_config %}
        {% endfor %}
    {% else %}
        {% for field in form %}
            {% include 'components/form_field.html' with field=field %}
        {% endfor %}
    {% endif %}
    
    <!-- Custom content -->
    {% if content %}
        {{ content|safe }}
    {% endif %}
    
    <!-- Form actions -->
    <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        {% if show_cancel %}
        <button 
            type="button" 
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            {% if cancel_url %}
                hx-get="{{ cancel_url }}"
                {% if cancel_target %}hx-target="{{ cancel_target }}"{% endif %}
            {% else %}
                onclick="history.back()"
            {% endif %}
        >
            {{ cancel_text|default:'Cancel' }}
        </button>
        {% endif %}
        
        {% if show_reset %}
        <button 
            type="reset" 
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            {{ reset_text|default:'Reset' }}
        </button>
        {% endif %}
        
        <button 
            type="submit" 
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            {% if submit_disabled %}disabled{% endif %}
        >
            <!-- Loading indicator -->
            <span class="htmx-indicator">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ submit_loading_text|default:'Saving...' }}
            </span>
            
            <!-- Default text -->
            <span class="htmx-indicator">
                {{ submit_text|default:'Save' }}
            </span>
        </button>
    </div>
    
    <!-- Form loading overlay -->
    {% if show_loading_overlay %}
    <div class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
        <div class="flex items-center space-x-2">
            <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm text-gray-600">{{ loading_text|default:'Processing...' }}</span>
        </div>
    </div>
    {% endif %}
</form>

<!-- Auto-save functionality -->
{% if auto_save %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('{{ form_id }}');
        if (form) {
            let autoSaveTimeout;
            
            form.addEventListener('input', function(e) {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(function() {
                    // Trigger auto-save
                    htmx.trigger(form, 'auto-save');
                }, {{ auto_save_delay|default:2000 }});
            });
        }
    });
</script>
{% endif %}

<!-- Form validation -->
{% if client_validation %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('{{ form_id }}');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Custom validation logic here
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                        
                        // Show error message
                        let errorMsg = field.parentNode.querySelector('.error-message');
                        if (!errorMsg) {
                            errorMsg = document.createElement('p');
                            errorMsg.className = 'error-message text-sm text-red-600 mt-1';
                            field.parentNode.appendChild(errorMsg);
                        }
                        errorMsg.textContent = 'This field is required.';
                    } else {
                        field.classList.remove('border-red-500');
                        const errorMsg = field.parentNode.querySelector('.error-message');
                        if (errorMsg) {
                            errorMsg.remove();
                        }
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    });
</script>
{% endif %}

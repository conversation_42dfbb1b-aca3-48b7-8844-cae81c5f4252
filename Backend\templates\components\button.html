<!-- Reusable HTMX Button Component -->
{% load django_htmx %}

<button 
    {% if hx_get %}hx-get="{{ hx_get }}"{% endif %}
    {% if hx_post %}hx-post="{{ hx_post }}"{% endif %}
    {% if hx_put %}hx-put="{{ hx_put }}"{% endif %}
    {% if hx_delete %}hx-delete="{{ hx_delete }}"{% endif %}
    {% if hx_target %}hx-target="{{ hx_target }}"{% endif %}
    {% if hx_swap %}hx-swap="{{ hx_swap }}"{% endif %}
    {% if hx_trigger %}hx-trigger="{{ hx_trigger }}"{% endif %}
    {% if hx_confirm %}hx-confirm="{{ hx_confirm }}"{% endif %}
    {% if hx_indicator %}hx-indicator="{{ hx_indicator }}"{% endif %}
    {% if hx_vals %}hx-vals="{{ hx_vals }}"{% endif %}
    {% if disabled %}disabled{% endif %}
    {% if id %}id="{{ id }}"{% endif %}
    {% if name %}name="{{ name }}"{% endif %}
    {% if value %}value="{{ value }}"{% endif %}
    {% if type %}type="{{ type }}"{% else %}type="button"{% endif %}
    class="
        inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200
        {% if variant == 'primary' %}
            text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500
        {% elif variant == 'secondary' %}
            text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500
        {% elif variant == 'success' %}
            text-white bg-green-600 hover:bg-green-700 focus:ring-green-500
        {% elif variant == 'danger' %}
            text-white bg-red-600 hover:bg-red-700 focus:ring-red-500
        {% elif variant == 'warning' %}
            text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500
        {% else %}
            text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500
        {% endif %}
        {% if size == 'sm' %}
            px-3 py-1.5 text-xs
        {% elif size == 'lg' %}
            px-6 py-3 text-base
        {% elif size == 'xl' %}
            px-8 py-4 text-lg
        {% endif %}
        {% if disabled %}
            opacity-50 cursor-not-allowed
        {% endif %}
        {{ class }}
    "
    {% if disabled %}aria-disabled="true"{% endif %}
>
    {% if loading_text %}
        <span class="htmx-indicator">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading_text }}
        </span>
    {% endif %}
    
    {% if icon %}
        <svg class="{% if text %}-ml-1 mr-2{% endif %} h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {% if icon == 'plus' %}
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            {% elif icon == 'edit' %}
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            {% elif icon == 'delete' %}
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            {% elif icon == 'save' %}
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
            {% elif icon == 'refresh' %}
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            {% endif %}
        </svg>
    {% endif %}
    
    <span class="{% if loading_text %}htmx-indicator{% endif %}">
        {{ text|default:slot }}
    </span>
</button>

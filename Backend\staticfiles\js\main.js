// TailAdmin JavaScript

// Initialize AlpineJS
document.addEventListener('alpine:init', () => {
    // Darkmode switcher
    Alpine.store('darkMode', {
        on: Alpine.$persist(false).as('darkMode'),
        toggle() {
            this.on = !this.on
            document.documentElement.classList.toggle('dark')
        }
    })

    // Sidebar state
    Alpine.store('sidebar', {
        isOpen: Alpine.$persist(true).as('sidebarOpen'),
        toggle() {
            this.isOpen = !this.isOpen
        }
    })

    // Dropdown state
    Alpine.data('dropdown', (initialOpenState = false) => ({
        open: initialOpenState,

        toggle() {
            this.open = !this.open
        },

        close() {
            this.open = false
        }
    }))

    // Notification state
    Alpine.store('notifications', {
        items: [],
        add(message, type = 'info') {
            const id = Date.now()
            this.items.push({ id, message, type })
            setTimeout(() => this.remove(id), 5000)
        },
        remove(id) {
            this.items = this.items.filter(item => item.id !== id)
        }
    })
})

// HTMX Integration
document.addEventListener('htmx:configRequest', (event) => {
    // Add CSRF token to HTMX requests
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value
    event.detail.headers['X-CSRFToken'] = csrfToken
})

// Initialize Persist plugin for AlpineJS
// Make sure Alpine.js and the persist plugin are loaded first

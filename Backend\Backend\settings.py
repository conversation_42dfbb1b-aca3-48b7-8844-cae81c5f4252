from pathlib import Path
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

NPM_BIN_PATH = r"C:\Program Files\nodejs\npm.cmd"

INSTALLED_APPS = [
    # Django built-in apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    'rest_framework_simplejwt',  # Only listed once now
    'django_rest_passwordreset',
    'django_browser_reload',
    'django_htmx',  # HTMX integration
    'drf_yasg',
    'corsheaders',
    'psycopg2',
    'tailwind',
    'theme',


    # Local apps in correct dependency order
    'core.apps.CoreConfig',
    'schools.apps.SchoolsConfig',
    'academics.apps.AcademicsConfig',
    'users.apps.UsersConfig',
    'fees.apps.FeesConfig',
    'analytics',

    # New apps for sidebar functionality
    'library',
    'inventory',
    'communication',
    'settings_app',
    'fleet.apps.FleetConfig',
    'community.apps.CommunityConfig',
    'mpesa_integration.apps.MpesaIntegrationConfig',
    'syllabus.apps.SyllabusConfig',
    'school_statistics',
    'profiles.apps.ProfilesConfig',
]

# Tailwind CSS settings
TAILWIND_APP_NAME = 'theme'
INTERNAL_IPS = [
    "127.0.0.1",
]

# Custom user model
AUTH_USER_MODEL = 'core.CustomUser'


# JWT Authentication settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=5),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}


# REST_FRAMEWORK = {
#     'DEFAULT_AUTHENTICATION_CLASSES': [
#         'rest_framework_simplejwt.authentication.JWTAuthentication',
#         'rest_framework.authentication.SessionAuthentication',
#     ],
#     'DEFAULT_SCHEMA_CLASS': 'drf_yasg.inspectors.SwaggerAutoSchema',
#     'DEFAULT_PERMISSION_CLASSES': [
#         'rest_framework.permissions.AllowAny',
#     ],
#     'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',
#     'DEFAULT_VERSION': 'v1',
#     'ALLOWED_VERSIONS': ['v1'],
#     'VERSION_PARAM': 'version',
# }


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# CORS settings - enhanced for better compatibility
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8000",
    "http://127.0.0.1:8000"
]

CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOW_CREDENTIALS = True

# Additional CORS settings for better compatibility
CORS_ALLOWED_METHODS = [
    'GET',
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
    'OPTIONS',
]

CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'cache-control',
    'pragma',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django_htmx.middleware.HtmxMiddleware',  # HTMX middleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django_browser_reload.middleware.BrowserReloadMiddleware',
    'settings_app.license_middleware.LicenseStatusMiddleware',  # License status update middleware
    'settings_app.middleware.ModuleAccessMiddleware',  # Module access control middleware
    'syllabus.middleware.CurriculumExceptionMiddleware',  # Add curriculum exception middleware
    'settings_app.user_middleware.CurrentUserMiddleware',  # Store current user for history tracking
]



TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',  # Main templates directory for reports and emails
            BASE_DIR / 'theme' / 'templates',  # Tailwind CSS theme templates (base templates)
            BASE_DIR / 'ShuleXcel' / 'templates',  # ShuleXcel app templates
        ],
        'APP_DIRS': True,  # This allows Django to look for templates in app directories
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# Add these settings if missing
SECRET_KEY = 'your-secret-key-here'
DEBUG = True
ALLOWED_HOSTS = []

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'theme' / 'static',  # Tailwind CSS compiled assets
    BASE_DIR / 'static',  # Additional static files
    BASE_DIR / 'ShuleXcel' / 'static',  # Admin template assets
]

# Media files (User uploaded files)
MEDIA_URL = 'media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'


# Make sure to add appropriate MIME types
FILE_UPLOAD_PERMISSIONS = 0o644

# Other required settings
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Add these essential URL settings
ROOT_URLCONF = 'Backend.urls'
WSGI_APPLICATION = 'Backend.wsgi.application'


# Authentication settings
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True



AUTHENTICATION_BACKENDS = [
    'core.authentication.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# Celery Configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Email Configuration for Alerts
# Using SMTP backend for real emails
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 465  # Try port 465 for SSL instead of 587 for TLS
EMAIL_USE_TLS = False
EMAIL_USE_SSL = True  # Use SSL instead of TLS
EMAIL_HOST_USER = '<EMAIL>'
# App password for Gmail (16-character password)
EMAIL_HOST_PASSWORD = 'jceuxiefuzcdnfnp'

# Fallback to console backend if SMTP fails
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Additional email settings
EMAIL_TIMEOUT = 60  # Increased timeout to 60 seconds
DEFAULT_FROM_EMAIL = 'ShuleXcel <<EMAIL>>'

# Swagger Settings
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
    'USE_SESSION_AUTH': False,
    'JSON_EDITOR': True,
    'SUPPORTED_SUBMIT_METHODS': ['get', 'post', 'put', 'delete', 'patch'],
    'OPERATIONS_SORTER': 'alpha',
    'TAGS_SORTER': 'alpha',
    'DOC_EXPANSION': 'none',
    'DEFAULT_MODEL_RENDERING': 'model',
    'DEEP_LINKING': True,
    'VALIDATOR_URL': None,
    'DEFAULT_INFO': None,
    'DEFAULT_AUTO_SCHEMA_CLASS': 'drf_yasg.inspectors.SwaggerAutoSchema',
    'SECURITY': [{'Bearer': []}],
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': True,
    },
    'REDOC_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': True,
    },
}

# Frontend URL for links in emails
FRONTEND_URL = 'http://localhost:5173/'

# Email settings are defined above
# Using SMTP backend with Gmail to send actual emails

# Default from email
DEFAULT_FROM_EMAIL = 'ShuleXcel <<EMAIL>>'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}

# JWT Settings
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': False,  # Changed to False to avoid issues with token rotation
    'BLACKLIST_AFTER_ROTATION': False,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# Logging Configuration - Simplified for development
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

# Cache configuration for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'syllabus-curriculum-cache',
        'TIMEOUT': 60 * 15,  # 15 minutes cache timeout
        'OPTIONS': {
            'MAX_ENTRIES': 1000
        }
    }
}

# Cache time to live is 15 minutes for curriculum data
CACHE_TTL = 60 * 15

# Cache keys
CURRICULUM_CACHE_KEYS = {
    'objective_suggestions': 'curriculum:suggestions:{}:{}',  # Format: syllabus_id:unit_id
    'coverage_report': 'curriculum:coverage:{}',  # Format: syllabus_id
    'alignment_analysis': 'curriculum:alignment:{}',  # Format: syllabus_id
}

# Django REST Password Reset settings
DJANGO_REST_PASSWORDRESET_SERIALIZER_CLASS = 'core.custom_serializers.CustomPasswordResetEmailSerializer'
DJANGO_REST_PASSWORDRESET_TOKEN_CONFIG = {
    'CLASS': 'django_rest_passwordreset.tokens.RandomStringTokenGenerator',
    'OPTIONS': {
        'min_length': 20,
        'max_length': 30
    }
}

# Token expiry time for password reset (in seconds)
# 24 hours = 86400 seconds
DJANGO_REST_MULTITOKENAUTH_RESET_TOKEN_EXPIRY_TIME = 86400  # in seconds, not timedelta
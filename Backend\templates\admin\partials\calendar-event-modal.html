<div
  class="fixed inset-0 items-center justify-center hidden p-5 overflow-y-auto modal z-99999"
  id="eventModal"
>
  <div
    class="modal-close-btn fixed inset-0 h-full w-full bg-gray-400/50 backdrop-blur-[32px]"
  ></div>
  <div
    class="modal-dialog modal-dialog-scrollable modal-lg no-scrollbar relative flex w-full max-w-[700px] flex-col overflow-y-auto rounded-3xl bg-white p-6 dark:bg-gray-900 lg:p-11"
  >
    <!-- close btn -->
    <button
      class="modal-close-btn transition-color absolute right-5 top-5 z-999 flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-gray-600 dark:bg-gray-700 dark:bg-white/[0.05] dark:text-gray-400 dark:hover:bg-white/[0.07] dark:hover:text-gray-300 sm:h-11 sm:w-11"
    >
      <svg
        class="fill-current"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.04289 16.5418C5.65237 16.9323 5.65237 17.5655 6.04289 17.956C6.43342 18.3465 7.06658 18.3465 7.45711 17.956L11.9987 13.4144L16.5408 17.9565C16.9313 18.347 17.5645 18.347 17.955 17.9565C18.3455 17.566 18.3455 16.9328 17.955 16.5423L13.4129 12.0002L17.955 7.45808C18.3455 7.06756 18.3455 6.43439 17.955 6.04387C17.5645 5.65335 16.9313 5.65335 16.5408 6.04387L11.9987 10.586L7.45711 6.04439C7.06658 5.65386 6.43342 5.65386 6.04289 6.04439C5.65237 6.43491 5.65237 7.06808 6.04289 7.4586L10.5845 12.0002L6.04289 16.5418Z"
          fill=""
        />
      </svg>
    </button>

    <div
      class="flex flex-col px-2 overflow-y-auto modal-content custom-scrollbar"
    >
      <div class="modal-header">
        <h5
          class="mb-2 font-semibold text-gray-800 modal-title text-theme-xl dark:text-white/90 lg:text-2xl"
          id="eventModalLabel"
        >
          Add / Edit Event
        </h5>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Plan your next big moment: schedule or edit an event to stay on track
        </p>
      </div>
      <div class="mt-8 modal-body">
        <div>
          <div>
            <div>
              <label
                class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
              >
                Event Title
              </label>
              <input
                id="event-title"
                type="text"
                class="dark:bg-dark-900 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
              />
            </div>
          </div>
          <div class="mt-6">
            <div>
              <label
                class="block mb-4 text-sm font-medium text-gray-700 dark:text-gray-400"
              >
                Event Color
              </label>
            </div>
            <div class="flex flex-wrap items-center gap-4 sm:gap-5">
              <div class="n-chk">
                <div class="form-check form-check-primary form-check-inline">
                  <label
                    class="flex items-center text-sm text-gray-700 form-check-label dark:text-gray-400"
                    for="modalDanger"
                  >
                    <span class="relative">
                      <input
                        class="sr-only form-check-input"
                        type="radio"
                        name="event-level"
                        value="Danger"
                        id="modalDanger"
                      />
                      <span
                        class="flex items-center justify-center w-5 h-5 mr-2 border border-gray-300 rounded-full box dark:border-gray-700"
                      >
                        <span
                          class="w-2 h-2 bg-white rounded-full dark:bg-transparent"
                        >
                        </span>
                      </span>
                    </span>
                    Danger
                  </label>
                </div>
              </div>
              <div class="n-chk">
                <div class="form-check form-check-warning form-check-inline">
                  <label
                    class="flex items-center text-sm text-gray-700 form-check-label dark:text-gray-400"
                    for="modalSuccess"
                  >
                    <span class="relative">
                      <input
                        class="sr-only form-check-input"
                        type="radio"
                        name="event-level"
                        value="Success"
                        id="modalSuccess"
                      />
                      <span
                        class="flex items-center justify-center w-5 h-5 mr-2 border border-gray-300 rounded-full box dark:border-gray-700"
                      >
                        <span
                          class="w-2 h-2 bg-white rounded-full dark:bg-transparent"
                        >
                        </span>
                      </span>
                    </span>
                    Success
                  </label>
                </div>
              </div>
              <div class="n-chk">
                <div class="form-check form-check-success form-check-inline">
                  <label
                    class="flex items-center text-sm text-gray-700 form-check-label dark:text-gray-400"
                    for="modalPrimary"
                  >
                    <span class="relative">
                      <input
                        class="sr-only form-check-input"
                        type="radio"
                        name="event-level"
                        value="Primary"
                        id="modalPrimary"
                      />
                      <span
                        class="flex items-center justify-center w-5 h-5 mr-2 border border-gray-300 rounded-full box dark:border-gray-700"
                      >
                        <span
                          class="w-2 h-2 bg-white rounded-full dark:bg-transparent"
                        >
                        </span>
                      </span>
                    </span>
                    Primary
                  </label>
                </div>
              </div>
              <div class="n-chk">
                <div class="form-check form-check-danger form-check-inline">
                  <label
                    class="flex items-center text-sm text-gray-700 form-check-label dark:text-gray-400"
                    for="modalWarning"
                  >
                    <span class="relative">
                      <input
                        class="sr-only form-check-input"
                        type="radio"
                        name="event-level"
                        value="Warning"
                        id="modalWarning"
                      />
                      <span
                        class="flex items-center justify-center w-5 h-5 mr-2 border border-gray-300 rounded-full box dark:border-gray-700"
                      >
                        <span
                          class="w-2 h-2 bg-white rounded-full dark:bg-transparent"
                        >
                        </span>
                      </span>
                    </span>
                    Warning
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6">
            <div>
              <label
                class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
              >
                Enter Start Date
              </label>
              <div class="relative">
                <input
                  id="event-start-date"
                  type="date"
                  class="dark:bg-dark-900 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pl-4 pr-11 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                  onclick="this.showPicker()"
                />
                <span class="absolute right-3.5 top-1/2 -translate-y-1/2">
                  <svg
                    class="fill-gray-700 dark:fill-gray-400"
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4.33317 0.0830078C4.74738 0.0830078 5.08317 0.418794 5.08317 0.833008V1.24967H8.9165V0.833008C8.9165 0.418794 9.25229 0.0830078 9.6665 0.0830078C10.0807 0.0830078 10.4165 0.418794 10.4165 0.833008V1.24967L11.3332 1.24967C12.2997 1.24967 13.0832 2.03318 13.0832 2.99967V4.99967V11.6663C13.0832 12.6328 12.2997 13.4163 11.3332 13.4163H2.6665C1.70001 13.4163 0.916504 12.6328 0.916504 11.6663V4.99967V2.99967C0.916504 2.03318 1.70001 1.24967 2.6665 1.24967L3.58317 1.24967V0.833008C3.58317 0.418794 3.91896 0.0830078 4.33317 0.0830078ZM4.33317 2.74967H2.6665C2.52843 2.74967 2.4165 2.8616 2.4165 2.99967V4.24967H11.5832V2.99967C11.5832 2.8616 11.4712 2.74967 11.3332 2.74967H9.6665H4.33317ZM11.5832 5.74967H2.4165V11.6663C2.4165 11.8044 2.52843 11.9163 2.6665 11.9163H11.3332C11.4712 11.9163 11.5832 11.8044 11.5832 11.6663V5.74967Z"
                      fill=""
                    />
                  </svg>
                </span>
              </div>
            </div>
          </div>

          <div class="mt-6">
            <div>
              <label
                class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
              >
                Enter End Date
              </label>
              <div class="relative">
                <input
                  id="event-end-date"
                  type="date"
                  class="dark:bg-dark-900 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pl-4 pr-11 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                  onclick="this.showPicker()"
                />
                <span class="absolute right-3.5 top-1/2 -translate-y-1/2">
                  <svg
                    class="fill-gray-700 dark:fill-gray-400"
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4.33317 0.0830078C4.74738 0.0830078 5.08317 0.418794 5.08317 0.833008V1.24967H8.9165V0.833008C8.9165 0.418794 9.25229 0.0830078 9.6665 0.0830078C10.0807 0.0830078 10.4165 0.418794 10.4165 0.833008V1.24967L11.3332 1.24967C12.2997 1.24967 13.0832 2.03318 13.0832 2.99967V4.99967V11.6663C13.0832 12.6328 12.2997 13.4163 11.3332 13.4163H2.6665C1.70001 13.4163 0.916504 12.6328 0.916504 11.6663V4.99967V2.99967C0.916504 2.03318 1.70001 1.24967 2.6665 1.24967L3.58317 1.24967V0.833008C3.58317 0.418794 3.91896 0.0830078 4.33317 0.0830078ZM4.33317 2.74967H2.6665C2.52843 2.74967 2.4165 2.8616 2.4165 2.99967V4.24967H11.5832V2.99967C11.5832 2.8616 11.4712 2.74967 11.3332 2.74967H9.6665H4.33317ZM11.5832 5.74967H2.4165V11.6663C2.4165 11.8044 2.52843 11.9163 2.6665 11.9163H11.3332C11.4712 11.9163 11.5832 11.8044 11.5832 11.6663V5.74967Z"
                      fill=""
                    />
                  </svg>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center gap-3 mt-6 modal-footer sm:justify-end">
        <button
          type="button"
          class="btn modal-close-btn bg-danger-subtle text-danger flex w-full justify-center rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] sm:w-auto"
          data-bs-dismiss="modal"
        >
          Close
        </button>
        <button
          type="button"
          class="btn btn-success btn-update-event flex w-full justify-center rounded-lg bg-brand-500 px-4 py-2.5 text-sm font-medium text-white hover:bg-brand-600 sm:w-auto"
          data-fc-event-public-id=""
        >
          Update changes
        </button>
        <button
          type="button"
          class="btn btn-primary btn-add-event flex w-full justify-center rounded-lg bg-brand-500 px-4 py-2.5 text-sm font-medium text-white hover:bg-brand-600 sm:w-auto"
        >
          Add Event
        </button>
      </div>
    </div>
  </div>
</div>

<!-- <div
  class="modal fixed left-0 top-0 z-99999 hidden min-h-screen w-full justify-center overflow-y-auto overflow-x-hidden bg-gray-400/50 px-4 py-4 backdrop-blur-[32px] lg:py-[120px]"
  id="eventModal"
>
</div> -->

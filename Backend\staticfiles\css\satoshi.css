/* <PERSON>shi <PERSON>ont Family */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('../fonts/Satoshi-Light.woff2') format('woff2'),
         url('../fonts/Satoshi-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: '<PERSON>shi';
    src: url('../fonts/Satoshi-Regular.woff2') format('woff2'),
         url('../fonts/Satoshi-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('../fonts/Satoshi-Medium.woff2') format('woff2'),
         url('../fonts/Satoshi-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('../fonts/Satoshi-Bold.woff2') format('woff2'),
         url('../fonts/Satoshi-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Apply <PERSON><PERSON> font to body */
body {
    font-family: 'Satoshi', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

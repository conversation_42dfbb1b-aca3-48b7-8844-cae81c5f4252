<!-- Reusable HTMX Table Component -->
{% load django_htmx %}

<div class="bg-white shadow overflow-hidden sm:rounded-md">
    <!-- Table header with search and actions -->
    {% if show_header %}
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    {{ title|default:'Data Table' }}
                </h3>
                {% if subtitle %}
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    {{ subtitle }}
                </p>
                {% endif %}
            </div>
            
            <div class="flex items-center space-x-3">
                <!-- Search -->
                {% if search_url %}
                <div class="relative">
                    <input 
                        type="text" 
                        name="search"
                        placeholder="{{ search_placeholder|default:'Search...' }}"
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        hx-get="{{ search_url }}"
                        hx-target="{{ search_target|default:'#table-body' }}"
                        hx-trigger="keyup changed delay:300ms"
                        hx-indicator="#search-loading"
                        hx-include="[name='page_size'], [name='sort_by'], [name='sort_order']"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div id="search-loading" class="htmx-indicator absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="animate-spin h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>
                {% endif %}
                
                <!-- Page size selector -->
                {% if show_page_size %}
                <select 
                    name="page_size"
                    class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    hx-get="{{ base_url }}"
                    hx-target="{{ table_target|default:'#table-container' }}"
                    hx-include="[name='search'], [name='sort_by'], [name='sort_order']"
                >
                    <option value="10" {% if page_size == 10 %}selected{% endif %}>10 per page</option>
                    <option value="25" {% if page_size == 25 %}selected{% endif %}>25 per page</option>
                    <option value="50" {% if page_size == 50 %}selected{% endif %}>50 per page</option>
                    <option value="100" {% if page_size == 100 %}selected{% endif %}>100 per page</option>
                </select>
                {% endif %}
                
                <!-- Action buttons -->
                {% if actions %}
                    {{ actions|safe }}
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Table -->
    <div id="{{ table_id|default:'table-container' }}" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <!-- Table header -->
            <thead class="bg-gray-50">
                <tr>
                    {% for column in columns %}
                    <th 
                        scope="col" 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
                               {% if column.sortable %}cursor-pointer hover:bg-gray-100{% endif %}"
                        {% if column.sortable %}
                        hx-get="{{ base_url }}"
                        hx-target="{{ table_target|default:'#table-container' }}"
                        hx-vals='{"sort_by": "{{ column.field }}", "sort_order": "{% if sort_by == column.field and sort_order == 'asc' %}desc{% else %}asc{% endif %}"}'
                        hx-include="[name='search'], [name='page_size']"
                        {% endif %}
                    >
                        <div class="flex items-center space-x-1">
                            <span>{{ column.label }}</span>
                            {% if column.sortable %}
                                {% if sort_by == column.field %}
                                    {% if sort_order == 'asc' %}
                                        <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                                        </svg>
                                    {% else %}
                                        <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    {% endif %}
                                {% else %}
                                    <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                    </svg>
                                {% endif %}
                            {% endif %}
                        </div>
                    </th>
                    {% endfor %}
                    {% if row_actions %}
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">Actions</span>
                    </th>
                    {% endif %}
                </tr>
            </thead>
            
            <!-- Table body -->
            <tbody id="{{ table_body_id|default:'table-body' }}" class="bg-white divide-y divide-gray-200">
                {% if rows %}
                    {% for row in rows %}
                    <tr class="hover:bg-gray-50 {% if row.highlight %}bg-yellow-50{% endif %}">
                        {% for column in columns %}
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if column.template %}
                                {% include column.template with object=row %}
                            {% else %}
                                {{ row|lookup:column.field }}
                            {% endif %}
                        </td>
                        {% endfor %}
                        
                        {% if row_actions %}
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                {% for action in row_actions %}
                                <button
                                    type="button"
                                    class="text-{{ action.color|default:'blue' }}-600 hover:text-{{ action.color|default:'blue' }}-900"
                                    {% if action.hx_get %}hx-get="{{ action.hx_get }}"{% endif %}
                                    {% if action.hx_post %}hx-post="{{ action.hx_post }}"{% endif %}
                                    {% if action.hx_delete %}hx-delete="{{ action.hx_delete }}"{% endif %}
                                    {% if action.hx_target %}hx-target="{{ action.hx_target }}"{% endif %}
                                    {% if action.hx_confirm %}hx-confirm="{{ action.hx_confirm }}"{% endif %}
                                    hx-vals='{"id": "{{ row.id }}"}'
                                >
                                    {{ action.label }}
                                </button>
                                {% endfor %}
                            </div>
                        </td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="{{ columns|length }}{% if row_actions %}1{% endif %}" class="px-6 py-4 text-center text-sm text-gray-500">
                            {{ empty_message|default:'No data available' }}
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if show_pagination and page_obj %}
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if page_obj.has_previous %}
            <button
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                hx-get="{{ base_url }}"
                hx-vals='{"page": "{{ page_obj.previous_page_number }}"}'
                hx-target="{{ table_target|default:'#table-container' }}"
                hx-include="[name='search'], [name='page_size'], [name='sort_by'], [name='sort_order']"
            >
                Previous
            </button>
            {% endif %}
            {% if page_obj.has_next %}
            <button
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                hx-get="{{ base_url }}"
                hx-vals='{"page": "{{ page_obj.next_page_number }}"}'
                hx-target="{{ table_target|default:'#table-container' }}"
                hx-include="[name='search'], [name='page_size'], [name='sort_by'], [name='sort_order']"
            >
                Next
            </button>
            {% endif %}
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ page_obj.start_index }}</span>
                    to
                    <span class="font-medium">{{ page_obj.end_index }}</span>
                    of
                    <span class="font-medium">{{ page_obj.paginator.count }}</span>
                    results
                </p>
            </div>
            
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page_obj.has_previous %}
                    <button
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        hx-get="{{ base_url }}"
                        hx-vals='{"page": "{{ page_obj.previous_page_number }}"}'
                        hx-target="{{ table_target|default:'#table-container' }}"
                        hx-include="[name='search'], [name='page_size'], [name='sort_by'], [name='sort_order']"
                    >
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <button
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                            hx-get="{{ base_url }}"
                            hx-vals='{"page": "{{ num }}"}'
                            hx-target="{{ table_target|default:'#table-container' }}"
                            hx-include="[name='search'], [name='page_size'], [name='sort_by'], [name='sort_order']"
                        >
                            {{ num }}
                        </button>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <button
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        hx-get="{{ base_url }}"
                        hx-vals='{"page": "{{ page_obj.next_page_number }}"}'
                        hx-target="{{ table_target|default:'#table-container' }}"
                        hx-include="[name='search'], [name='page_size'], [name='sort_by'], [name='sort_order']"
                    >
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

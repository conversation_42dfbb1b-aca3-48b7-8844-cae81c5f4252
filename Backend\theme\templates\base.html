{% load static tailwind_tags django_htmx %}
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="htmx-config" content='{"globalViewTransitions": true, "refreshOnHistoryMiss": true}'>

    <title>{% block title %}ShuleXcel - School Management System{% endblock %}</title>

    <!-- Tailwind CSS -->
    {% tailwind_css %}

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    {% django_htmx_script %}

    <!-- HTMX Extensions -->
    <script src="https://unpkg.com/htmx-ext-loading-states@2.0.0/loading-states.js"></script>
    <script src="https://unpkg.com/htmx-ext-debug@2.0.0/debug.js"></script>

    <!-- Alpine.js for enhanced interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom CSS -->
    <style>
        /* HTMX Loading States */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        .htmx-request.htmx-indicator {
            opacity: 1;
        }

        /* Custom loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth transitions for HTMX swaps */
        .htmx-settling {
            transition: all 300ms ease-in-out;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body class="h-full bg-gray-50 font-sans antialiased" hx-ext="loading-states{% if debug %},debug{% endif %}">
    <!-- Global Loading Indicator -->
    <div id="global-loading" class="htmx-indicator fixed top-4 right-4 z-50">
        <div class="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <div class="spinner"></div>
            <span>Loading...</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="h-full flex flex-col">
        {% block header %}
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">
                            {% block header_title %}ShuleXcel{% endblock %}
                        </h1>
                    </div>
                    <nav class="flex space-x-4">
                        {% block navigation %}
                        <a href="#" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Dashboard
                        </a>
                        <a href="#" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Students
                        </a>
                        <a href="#" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                            Teachers
                        </a>
                        {% endblock %}
                    </nav>
                </div>
            </div>
        </header>
        {% endblock %}

        <!-- Main Content Area -->
        <main class="flex-1 overflow-hidden">
            {% block content %}
            <div class="h-full flex items-center justify-center">
                <div class="text-center">
                    <h1 class="text-6xl font-bold text-gray-900 mb-4">
                        Django + HTMX + Tailwind
                    </h1>
                    <p class="text-xl text-gray-600 mb-8">
                        Perfect stack for modern web development
                    </p>
                    <div class="flex justify-center space-x-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            Django 5.2.1
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            HTMX 2.0.4
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                            Tailwind CSS 3.4
                        </span>
                    </div>
                </div>
            </div>
            {% endblock %}
        </main>

        {% block footer %}
        <footer class="bg-white border-t border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <p class="text-center text-sm text-gray-500">
                    © 2025 ShuleXcel. Built with Django, HTMX, and Tailwind CSS.
                </p>
            </div>
        </footer>
        {% endblock %}
    </div>

    <!-- Toast Notifications Container -->
    <div id="toast-container" class="fixed top-4 left-4 z-50 space-y-2">
        <!-- Toasts will be inserted here via HTMX -->
    </div>

    <!-- Modal Container -->
    <div id="modal-container">
        <!-- Modals will be inserted here via HTMX -->
    </div>

    <!-- Custom JavaScript -->
    <script>
        // Global HTMX event handlers
        document.addEventListener('htmx:beforeRequest', function(evt) {
            console.log('HTMX Request starting:', evt.detail);
        });

        document.addEventListener('htmx:afterRequest', function(evt) {
            console.log('HTMX Request completed:', evt.detail);
        });

        document.addEventListener('htmx:responseError', function(evt) {
            console.error('HTMX Response error:', evt.detail);
            // Show error toast
            showToast('An error occurred. Please try again.', 'error');
        });

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast-${type} px-4 py-2 rounded-lg shadow-lg text-white transform transition-all duration-300 translate-x-full`;

            const bgColor = {
                'info': 'bg-blue-500',
                'success': 'bg-green-500',
                'warning': 'bg-yellow-500',
                'error': 'bg-red-500'
            }[type] || 'bg-blue-500';

            toast.classList.add(bgColor);
            toast.textContent = message;

            const container = document.getElementById('toast-container');
            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 5000);
        }

        // Initialize any Alpine.js components
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized');
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
